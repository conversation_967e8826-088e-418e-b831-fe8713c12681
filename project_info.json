{"project_name": "Outlook Summary System", "last_updated": "2025-08-02 14:56:58", "update_source": "claude_<PERSON>", "statistics": {"total_files": 21429, "python_files": 380, "test_files": 70, "doc_files": 1488, "function_count": 417, "class_count": 729, "commit_count": "151", "contributors": 3}, "git_info": {"current_branch": "main", "last_commit": "5989876 - docs: Add WebSocket API guide and updated task tracking for dashboard monitoring"}, "generated_at": "2025-08-02T14:57:15.131702"}