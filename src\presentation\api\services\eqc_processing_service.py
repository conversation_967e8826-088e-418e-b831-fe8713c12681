"""
EQC 處理服務模組 - 階段二重構版本
整合所有 EQC 相關處理邏輯，提供統一的 EQC 處理服務

此版本使用模組化架構，將原始的大型方法拆分為專門的處理器類，
但保持完全相同的 API 接口和功能行為。
"""

import os
import sys
import time
import asyncio
from typing import Dict, Any
from pathlib import Path
from loguru import logger
from fastapi import HTTPException
from fastapi.responses import JSONResponse

from .api_utils import (
    APIUtils, 
    SystemConfig,
    LoggingUtils, 
    ResponseFormatter
)
from ..models import (
    EQCBin1ScanRequest,
    EQCBin1ScanResponse,
    OnlineEQCProcessRequest,
    OnlineEQCProcessResponse,
    OnlineEQCProcessData,
    EQCStep5TestFlowRequest,
    EQCStep5TestFlowResponse
)

# 導入重構後的處理器
from .eqc_endpoint_manager import EQCEndpointManager


class EQCProcessingService:
    """EQC 處理服務類別 - 重構版本
    
    此類保持與原始版本完全相同的接口，但內部使用模組化的處理器架構。
    所有方法的行為、參數和回應格式都與原版本完全一致。
    """

    # 超時設定 (秒) - 與原版本相同
    DEFAULT_TIMEOUT = 300  # 5分鐘
    LONG_OPERATION_TIMEOUT = 600  # 10分鐘

    def __init__(self):
        """初始化 EQC 處理服務"""
        self._endpoint_manager = None

    @property
    def endpoint_manager(self) -> EQCEndpointManager:
        """獲取端點管理器實例（懶加載）"""
        if self._endpoint_manager is None:
            self._endpoint_manager = EQCEndpointManager()
        return self._endpoint_manager

    async def _with_timeout(self, coro, timeout_seconds: int = None):
        """為協程添加超時機制 - 與原版本相同"""
        if timeout_seconds is None:
            timeout_seconds = self.DEFAULT_TIMEOUT

        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            logger.error(f"[ALARM_CLOCK] 操作超時 ({timeout_seconds}秒)")
            raise HTTPException(
                status_code=408,
                detail=f"處理超時，請檢查資料量是否過大或系統負載。超時限制: {timeout_seconds}秒"
            )
    
    async def scan_eqc_bin1(self, request: EQCBin1ScanRequest) -> EQCBin1ScanResponse:
        """掃描 EQC BIN=1 資料 - 保持與原版本完全相同的行為"""
        try:
            LoggingUtils.log_api_start("scan_eqc_bin1", {"folder_path": request.folder_path})
            
            original_path, folder_path = APIUtils.process_folder_path(request.folder_path)
            
            # 驗證資料夾
            is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
            if not is_valid:
                return EQCBin1ScanResponse(
                    status="error",
                    message=error_msg
                )
            
            # 使用原始的處理邏輯（保持完全一致）
            from src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import EQCBin1FinalProcessorV2
            processor = EQCBin1FinalProcessorV2()
            
            # 執行掃描
            scan_result = processor.scan_eqc_bin1_files(folder_path)
            
            LoggingUtils.log_api_success("scan_eqc_bin1", f"掃描完成，找到 {scan_result.get('file_count', 0)} 個檔案")
            
            return EQCBin1ScanResponse(
                status="success",
                message=f"掃描完成，找到 {scan_result.get('file_count', 0)} 個 EQC BIN=1 檔案",
                data=scan_result
            )
            
        except Exception as e:
            return self._handle_error("scan_eqc_bin1", e)
    
    async def process_online_eqc(self, request: OnlineEQCProcessRequest) -> OnlineEQCProcessResponse:
        """
        完整 Online EQC 處理，支援三種模式：1=EQC BIN1統計, 2=EQCTOTALDATA生成, 3=同時執行
        
        委託給重構後的線上處理器，但保持完全相同的接口和行為。
        """
        try:
            return await self.endpoint_manager.process_online_eqc(request)
        except Exception as e:
            return self._handle_error("process_online_eqc", e)
    
    async def process_eqc_standard(self, request: dict) -> dict:
        """EQC 標準處理端點，與模組化前端完全兼容
        
        委託給重構後的標準處理器，但保持完全相同的接口和行為。
        """
        try:
            # 將字典轉換為 Pydantic 模型
            from ..models import EQCStandardProcessRequest
            pydantic_request = EQCStandardProcessRequest(**request)
            
            # 委託給端點管理器處理
            result = await self.endpoint_manager.process_eqc_standard(pydantic_request)
            
            # 轉換回字典格式以保持向後兼容性
            if hasattr(result, 'dict'):
                return result.dict()
            else:
                return result
                
        except Exception as e:
            return self._handle_dict_error("process_eqc_standard", e)
    
    async def process_eqc_advanced(self, request: dict) -> dict:
        """EQC 進階處理端點 - Step 2: 純粹第二階段處理器 (程式碼區間檢測與雙重搜尋)
        
        委託給重構後的進階處理器，但保持完全相同的接口和行為。
        """
        try:
            # 將字典轉換為 Pydantic 模型
            from ..models import EQCStandardProcessRequest
            pydantic_request = EQCStandardProcessRequest(**request)
            
            # 委託給端點管理器處理
            result = await self.endpoint_manager.process_eqc_advanced(pydantic_request)
            
            # 轉換回字典格式以保持向後兼容性
            if hasattr(result, 'dict'):
                return result.dict()
            else:
                return result
                
        except Exception as e:
            return self._handle_dict_error("process_eqc_advanced", e)
    
    async def generate_test_flow(self, request: EQCStep5TestFlowRequest) -> EQCStep5TestFlowResponse:
        """EQC Step 5 測試流程生成，解析 DEBUG LOG 生成線性測試流程
        
        委託給重構後的測試流程處理器，但保持完全相同的接口和行為。
        """
        try:
            return await self.endpoint_manager.generate_test_flow(request)
        except Exception as e:
            return self._handle_error("generate_test_flow", e)
    
    async def analyze_real_data(self, request: dict) -> dict:
        """分析真實 EQCTOTALDATA.xlsx 資料，提供詳細統計資訊

        保持與原版本完全相同的實現。
        """
        try:
            LoggingUtils.log_api_start("analyze_real_data", {
                "folder_path": request.get('folder_path')
            })

            original_path, folder_path = APIUtils.process_folder_path(request.get('folder_path', ''))

            # 驗證資料夾
            is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
            if not is_valid:
                return {"status": "error", "message": error_msg}

            # 檢查 EQCTOTALDATA.xlsx 是否存在
            excel_path = os.path.join(folder_path, "EQCTOTALDATA.xlsx")
            if not os.path.exists(excel_path):
                return {
                    "status": "error",
                    "message": f"EQCTOTALDATA.xlsx 不存在於 {original_path}"
                }

            # 讀取和分析 Excel 檔案（按照原版本邏輯）
            import pandas as pd
            df = pd.read_excel(excel_path, header=None)

            online_eqc_fail = int(df.iloc[8, 1])  # B9 - Online EQC FAIL
            eqc_rt_pass = int(df.iloc[9, 1])      # B10 - EQC RT PASS
            total_rows_from_14 = len(df) - 13     # 從第14行開始的總行數

            # 解析 Summary sheet（關鍵：這裡包含 Site 分布數據）
            from .api_utils import DataParser
            summary_data = DataParser.parse_summary_sheet_data(excel_path)

            # 分析 Debug 日誌
            debug_file = os.path.join(folder_path, "EQCTOTALDATA_Step4_DEBUG.log")
            debug_matches = DataParser.count_debug_matches(debug_file)
            total_matches = debug_matches + online_eqc_fail

            # 計算匹配率
            match_rate = 100 if online_eqc_fail > 0 else 0

            # 判斷搜尋方法和狀態
            search_method = "主要區間"
            search_status = "成功" if os.path.exists(excel_path) else "失敗"

            LoggingUtils.log_api_success("analyze_real_data", f"分析完成: FAIL={online_eqc_fail}, PASS={eqc_rt_pass}")

            # 按照原版本格式返回，前端期望的扁平結構
            return {
                "status": "success",
                "online_eqc_fail": online_eqc_fail,        # 實際的 FAIL 數量
                "eqc_rt_pass": eqc_rt_pass,                # 實際的 PASS 數量
                "match_rate": f"{match_rate}%",            # 計算的匹配率
                "total_matches": total_matches,            # 總匹配數量 (Debug + FAIL)
                "total_rows": total_rows_from_14,          # 從第14行開始的總行數
                "matched_count": online_eqc_fail,          # 成功匹配的數量
                "required_count": online_eqc_fail,         # 需要匹配的數量
                "search_method": search_method,            # 搜尋方法
                "search_status": search_status,            # 搜尋狀態
                "folder_path": original_path,
                "debug_matches": debug_matches,
                "summary_data": summary_data               # 詳細的 Summary 統計資料（包含 site_stats）
            }

        except Exception as e:
            return self._handle_dict_error("analyze_real_data", e)
    
    def _handle_error(self, operation: str, error: Exception) -> EQCBin1ScanResponse:
        """處理 EQC 操作錯誤（回傳 Response 模型） - 與原版本相同"""
        LoggingUtils.log_api_error(operation, error)
        return EQCBin1ScanResponse(
            status="error",
            message=f"EQC {operation} 處理失敗: {str(error)}"
        )
    
    def _handle_dict_error(self, operation: str, error: Exception) -> dict:
        """處理 EQC 操作錯誤（回傳字典） - 與原版本相同"""
        LoggingUtils.log_api_error(operation, error)
        return {
            "status": "error",
            "message": f"EQC {operation} 處理失敗: {str(error)}"
        }
    
    def _process_chinese_paths(self, folder_path: str) -> bool:
        """處理中文路徑與特殊符號 - 與原版本相同"""
        try:
            from src.infrastructure.adapters.filesystem.chinese_path_processor import process_chinese_paths_in_directory
            success = process_chinese_paths_in_directory(folder_path, verbose=False)
            if success:
                logger.info("   [OK] 路徑標準化完成")
            else:
                logger.warning("   [WARNING] 路徑標準化失敗，但繼續執行")
            return success
        except Exception as e:
            logger.warning(f"[WARNING] 中文路徑處理失敗: {e}")
            return False


# 全域 EQC 處理服務實例 - 保持與原版本相同
eqc_processing_service = EQCProcessingService()


# 依賴注入函數 - 保持與原版本完全相同
def get_eqc_processing_service() -> EQCProcessingService:
    """取得 EQC 處理服務實例（用於 FastAPI 依賴注入）"""
    return eqc_processing_service
