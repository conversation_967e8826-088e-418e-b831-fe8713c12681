"""
EQC 標準處理器 - 專門處理標準 EQC 流程
階段二重構：高優先級端點重構實現

此模組負責處理標準的 EQC 處理流程，支援兩種模式：
1. 完整流程（包含 Step 1-2-3）
2. 僅第二階段處理
"""

import os
import time
import logging
from typing import Dict, Any, Optional, Tuple

from src.presentation.api.services.api_utils import APIUtils, LoggingUtils

logger = logging.getLogger(__name__)


class EQCStandardHandler:
    """EQC 標準處理器"""
    
    def __init__(self):
        self.logger = logger
        
    async def process_standard_eqc(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        執行 EQC 標準處理流程
        
        Args:
            request_data: 包含處理參數的字典
            
        Returns:
            處理結果字典
        """
        try:
            start_time = time.time()
            
            LoggingUtils.log_api_start("process_eqc_standard", {
                "folder_path": request_data.get('folder_path'),
                "include_step123": request_data.get('include_step123', True)
            })
            
            # 路徑處理與驗證
            folder_path, validation_result = await self._prepare_processing_environment(request_data)
            if validation_result.get('error'):
                return validation_result
            
            # 提取處理參數
            include_step123 = request_data.get('include_step123', True)
            code_regions = self._extract_code_regions(request_data)
            
            # 執行標準處理流程
            if include_step123:
                result = await self._execute_complete_pipeline(folder_path, code_regions)
            else:
                result = await self._execute_stage2_only(folder_path, code_regions)
            
            # 計算處理時間
            processing_time = time.time() - start_time
            result['processing_time'] = round(processing_time, 2)
            
            LoggingUtils.log_api_success("process_eqc_standard", f"處理完成，耗時 {processing_time:.2f}秒")
            
            return result
            
        except Exception as e:
            self.logger.error(f"EQC 標準處理失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"EQC 標準處理失敗: {str(e)}"
            }
    
    async def _prepare_processing_environment(self, request_data: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """準備處理環境"""
        try:
            original_path, folder_path = APIUtils.process_folder_path(request_data.get('folder_path', ''))
            
            # 驗證資料夾
            is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
            if not is_valid:
                return folder_path, {"status": "error", "message": error_msg}
            
            self.logger.info(f"[FOLDER] 處理路徑: {original_path} -> {folder_path}")
            return folder_path, {"status": "success"}
            
        except Exception as e:
            return "", {"status": "error", "message": f"路徑處理失敗: {str(e)}"}
    
    def _extract_code_regions(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取 CODE 區間設定"""
        code_regions = {
            'main_start': request_data.get('main_start'),
            'main_end': request_data.get('main_end'),
            'backup_start': request_data.get('backup_start'),
            'backup_end': request_data.get('backup_end')
        }
        
        # 過濾掉 None 值
        code_regions = {k: v for k, v in code_regions.items() if v is not None}
        
        if code_regions:
            self.logger.info(f"[OK] 使用指定的 CODE 區間設定: {code_regions}")
            return code_regions
        else:
            self.logger.info("[SEARCH] 未指定 CODE 區間，將使用自動檢測")
            return None
    
    async def _execute_complete_pipeline(self, folder_path: str, code_regions: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """執行完整處理流程（包含 Step 1-2-3）"""
        try:
            self.logger.info("[ROCKET] 執行完整 EQC 標準處理流程")
            
            from src.infrastructure.adapters.excel.eqc.processors.eqc_standard_processor import StandardEQCProcessor
            processor = StandardEQCProcessor()
            
            result = processor.process_code_comparison_pipeline(folder_path, code_regions=code_regions)
            
            if result['status'] == 'success':
                self.logger.info("[OK] 完整處理流程執行成功")
            else:
                self.logger.error(f"[ERROR] 完整處理流程失敗: {result.get('message', '未知錯誤')}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"完整處理流程執行失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"完整處理流程執行失敗: {str(e)}"
            }
    
    async def _execute_stage2_only(self, folder_path: str, code_regions: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """執行僅第二階段處理"""
        try:
            self.logger.info("[REFRESH] 執行第二階段處理（跳過 Step 1-2-3）")
            
            from src.infrastructure.adapters.excel.eqc.processors.eqc_standard_processor import StandardEQCProcessor
            processor = StandardEQCProcessor()
            
            result = processor.process_from_stage2_only(
                folder_path, 
                include_inseqcrtdata2=True,
                include_step5_testflow=True,
                include_step6_excel=True,
                include_final_excel_conversion=True,
                code_regions=code_regions
            )
            
            if result['status'] == 'success':
                self.logger.info("[OK] 第二階段處理執行成功")
            else:
                self.logger.error(f"[ERROR] 第二階段處理失敗: {result.get('message', '未知錯誤')}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"第二階段處理執行失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"第二階段處理執行失敗: {str(e)}"
            }


class EQCTestFlowHandler:
    """EQC 測試流程處理器 - 專門處理 Step 5 測試流程生成"""
    
    def __init__(self):
        self.logger = logger
    
    async def generate_test_flow(self, doc_directory: str) -> Dict[str, Any]:
        """
        生成 EQC Step 5 測試流程
        
        Args:
            doc_directory: 文檔目錄路徑
            
        Returns:
            測試流程生成結果
        """
        try:
            start_time = time.time()
            
            LoggingUtils.log_api_start("generate_test_flow", {
                "doc_directory": doc_directory
            })
            
            original_path, doc_directory = APIUtils.process_folder_path(doc_directory)
            
            # 驗證資料夾
            is_valid, error_msg = APIUtils.validate_folder_path(doc_directory)
            if not is_valid:
                return {
                    "status": "error",
                    "message": error_msg,
                    "error_message": "指定的資料夾路徑不存在"
                }
            
            # 檢查必要檔案
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            if not os.path.exists(eqctotaldata_path):
                return {
                    "status": "error",
                    "message": "未找到 EQCTOTALDATA.csv 檔案",
                    "error_message": "請先執行 Step 1-4 生成 EQCTOTALDATA.csv"
                }
            
            # 導入並執行 Step 5 處理器
            from src.infrastructure.adapters.excel.eqc.eqc_step5_testflow_processor import EQCStep5TestFlowProcessor
            
            processor = EQCStep5TestFlowProcessor()
            result = processor.generate_test_flow(doc_directory)
            
            processing_time = time.time() - start_time
            
            LoggingUtils.log_api_success("generate_test_flow", f"處理完成，耗時 {processing_time:.2f}秒")
            
            return {
                "status": "success",
                "message": "Step 5 測試流程生成完成",
                "processing_time": round(processing_time, 2),
                **result
            }
            
        except Exception as e:
            self.logger.error(f"測試流程生成失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"測試流程生成失敗: {str(e)}"
            }
