"""
EQC 端點管理器 - 統一協調所有 EQC 相關端點的處理邏輯
階段二重構：高優先級端點重構實現

此模組作為所有 EQC 端點的統一入口，負責：
1. 協調不同類型的 EQC 處理流程
2. 統一錯誤處理和日誌記錄
3. 提供一致的回應格式
4. 管理處理器實例的生命週期
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

from src.presentation.api.models import (
    OnlineEQCProcessRequest, OnlineEQCProcessResponse,
    EQCStandardProcessRequest, EQCStandardProcessResponse,
    EQCAdvancedProcessResponse, EQCStep5TestFlowRequest, EQCStep5TestFlowResponse
)
from src.presentation.api.services.eqc_advanced_processor import EQCAdvancedProcessor
from src.presentation.api.services.eqc_standard_handler import EQCStandardHandler, EQCTestFlowHandler
from src.presentation.api.services.eqc_online_handler import EQCOnlineHandler
from src.presentation.api.services.api_utils import ResponseFormatter, LoggingUtils

logger = logging.getLogger(__name__)


@dataclass
class ProcessingMetrics:
    """處理指標"""
    start_time: float
    end_time: Optional[float] = None
    success_count: int = 0
    error_count: int = 0
    
    @property
    def processing_time(self) -> float:
        if self.end_time:
            return self.end_time - self.start_time
        return 0.0


class EQCEndpointManager:
    """EQC 端點管理器 - 統一協調所有 EQC 相關端點"""
    
    def __init__(self):
        self.logger = logger
        self._advanced_processor = None
        self._standard_handler = None
        self._online_handler = None
        self._testflow_handler = None
        self._response_formatter = None
        self._metrics = ProcessingMetrics(start_time=0)
    
    @property
    def advanced_processor(self) -> EQCAdvancedProcessor:
        """獲取進階處理器實例（懶加載）"""
        if self._advanced_processor is None:
            self._advanced_processor = EQCAdvancedProcessor()
        return self._advanced_processor
    
    @property
    def standard_handler(self) -> EQCStandardHandler:
        """獲取標準處理器實例（懶加載）"""
        if self._standard_handler is None:
            self._standard_handler = EQCStandardHandler()
        return self._standard_handler
    
    @property
    def online_handler(self) -> EQCOnlineHandler:
        """獲取線上處理器實例（懶加載）"""
        if self._online_handler is None:
            self._online_handler = EQCOnlineHandler()
        return self._online_handler
    
    @property
    def testflow_handler(self) -> EQCTestFlowHandler:
        """獲取測試流程處理器實例（懶加載）"""
        if self._testflow_handler is None:
            self._testflow_handler = EQCTestFlowHandler()
        return self._testflow_handler
    
    @property
    def response_formatter(self) -> ResponseFormatter:
        """獲取回應格式化器實例（懶加載）"""
        if self._response_formatter is None:
            self._response_formatter = ResponseFormatter()
        return self._response_formatter
    
    async def process_eqc_advanced(self, request: EQCStandardProcessRequest) -> dict:
        """處理 EQC 進階流程 (雙階段處理) - 返回字典格式以保持向後兼容性"""
        try:
            self.logger.info("[MANAGER] 開始處理 EQC 進階流程")

            # 將 Pydantic 模型轉換為 dict
            result = await self.advanced_processor.process_advanced_eqc(request.dict())

            # 檢查處理是否成功
            if result.get('status') != 'success':
                error_message = f"EQC 進階處理失敗: {result.get('message', '未知錯誤')}"
                self.logger.error(f"[MANAGER] {error_message}")
                self._metrics.error_count += 1
                return {
                    "status": "error",
                    "message": error_message
                }

            # advanced_processor 已經返回了正確的格式，包含 integrated_result.results
            # 直接返回結果，不需要額外處理
            self._metrics.success_count += 1
            self.logger.info("[MANAGER] EQC 進階流程處理成功")
            return result
                
        except Exception as e:
            self._metrics.error_count += 1
            self.logger.error(f"[MANAGER] EQC 進階處理失敗: {str(e)}")
            raise
    
    async def process_eqc_standard(self, request: EQCStandardProcessRequest) -> EQCStandardProcessResponse:
        """處理 EQC 標準流程"""
        try:
            self.logger.info("[MANAGER] 開始處理 EQC 標準流程")
            
            # 將 Pydantic 模型轉換為 dict
            result = await self.standard_handler.process_standard_eqc(request.dict())
            
            # 格式化回應
            formatted_response = self.response_formatter.format_eqc_standard_response(result)
            
            self._metrics.success_count += 1
            self.logger.info("[MANAGER] EQC 標準流程處理成功")
            return formatted_response
            
        except Exception as e:
            self._metrics.error_count += 1
            self.logger.error(f"[MANAGER] EQC 標準處理失敗: {str(e)}")
            raise
    
    async def process_online_eqc(self, request: OnlineEQCProcessRequest) -> OnlineEQCProcessResponse:
        """處理線上 EQC 流程"""
        try:
            self.logger.info("[MANAGER] 開始處理線上 EQC 流程")
            
            # 直接使用 Pydantic 模型
            result = await self.online_handler.process_online_eqc(request)
            
            self._metrics.success_count += 1
            self.logger.info("[MANAGER] 線上 EQC 流程處理成功")
            return result
            
        except Exception as e:
            self._metrics.error_count += 1
            self.logger.error(f"[MANAGER] 線上 EQC 處理失敗: {str(e)}")
            raise
    
    async def generate_test_flow(self, request: EQCStep5TestFlowRequest) -> EQCStep5TestFlowResponse:
        """生成 EQC Step 5 測試流程"""
        try:
            self.logger.info("[MANAGER] 開始生成 EQC 測試流程")
            
            result = await self.testflow_handler.generate_test_flow(request.doc_directory)
            
            # 構建回應
            response = EQCStep5TestFlowResponse(
                status=result.get("status", "success"),
                message=result.get("message", "測試流程生成完成"),
                **{k: v for k, v in result.items() if k not in ["status", "message"]}
            )
            
            self._metrics.success_count += 1
            self.logger.info("[MANAGER] EQC 測試流程生成成功")
            return response
            
        except Exception as e:
            self._metrics.error_count += 1
            self.logger.error(f"[MANAGER] EQC 測試流程生成失敗: {str(e)}")
            raise
    
    def get_processing_metrics(self) -> Dict[str, Any]:
        """獲取處理指標"""
        return {
            "success_count": self._metrics.success_count,
            "error_count": self._metrics.error_count,
            "total_requests": self._metrics.success_count + self._metrics.error_count,
            "success_rate": (
                self._metrics.success_count / (self._metrics.success_count + self._metrics.error_count)
                if (self._metrics.success_count + self._metrics.error_count) > 0 else 0
            )
        }
    
    def reset_metrics(self):
        """重置處理指標"""
        self._metrics = ProcessingMetrics(start_time=0)
        self.logger.info("[MANAGER] 處理指標已重置")


# 全域端點管理器實例
eqc_endpoint_manager = EQCEndpointManager()


def get_eqc_endpoint_manager() -> EQCEndpointManager:
    """獲取 EQC 端點管理器實例（用於 FastAPI 依賴注入）"""
    return eqc_endpoint_manager
