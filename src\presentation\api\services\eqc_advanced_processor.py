"""
EQC 進階處理器 - 專門處理雙階段 EQC 流程
階段二重構：高優先級端點重構實現

此模組負責協調 EQC 進階處理的兩個階段：
1. 第一階段：EQC Bin1 Final Processor - 生成 EQCTOTALDATA.csv
2. 第二階段：Standard EQC Processor - 程式碼區間檢測與雙重搜尋
"""

import os
import time
import logging
from typing import Dict, Any, Optional, Tuple

from src.presentation.api.services.api_utils import APIUtils, LoggingUtils

logger = logging.getLogger(__name__)


class EQCAdvancedProcessor:
    """EQC 進階處理器 - 雙階段處理協調器"""
    
    def __init__(self):
        self.logger = logger
        
    async def process_advanced_eqc(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        執行 EQC 進階處理 - 雙階段流程
        
        Args:
            request_data: 包含處理參數的字典
            
        Returns:
            處理結果字典
        """
        try:
            start_time = time.time()
            
            LoggingUtils.log_api_start("process_eqc_advanced", {
                "folder_path": request_data.get('folder_path'),
                "stage": "2_stage_processing"
            })
            
            # 路徑處理與驗證
            folder_path, validation_result = await self._prepare_processing_environment(request_data)
            if validation_result.get('error'):
                return validation_result
            
            # 提取並處理 CODE 區間設定
            code_regions = self._extract_code_regions(request_data)
            
            # 第一階段：生成 EQCTOTALDATA.csv
            stage1_result = await self._execute_stage1_processing(folder_path)
            if stage1_result.get('status') == 'error':
                return stage1_result
            
            # 第二階段：程式碼區間檢測與雙重搜尋
            stage2_result = await self._execute_stage2_processing(folder_path, code_regions)
            if stage2_result.get('status') == 'error':
                return stage2_result
            
            # 執行後處理（CSV to Summary）
            summary_result = await self._execute_post_processing(folder_path)
            
            # 計算總處理時間
            processing_time = time.time() - start_time
            
            # 構建最終結果
            return self._build_final_result(
                stage1_result, stage2_result, summary_result, processing_time, code_regions
            )
            
        except Exception as e:
            self.logger.error(f"EQC 進階處理失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"EQC 進階處理失敗: {str(e)}"
            }
    
    async def _prepare_processing_environment(self, request_data: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """準備處理環境"""
        try:
            original_path, folder_path = APIUtils.process_folder_path(request_data.get('folder_path', ''))
            
            # 驗證資料夾
            is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
            if not is_valid:
                return folder_path, {"status": "error", "message": error_msg}
            
            self.logger.info(f"[FOLDER] 處理路徑: {original_path} -> {folder_path}")
            return folder_path, {"status": "success"}
            
        except Exception as e:
            return "", {"status": "error", "message": f"路徑處理失敗: {str(e)}"}
    
    def _extract_code_regions(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取 CODE 區間設定"""
        code_regions = {}
        
        # 處理主要區間
        main_region = request_data.get('main_region')
        if main_region:
            code_regions['main_start'] = main_region.get('start_column')
            code_regions['main_end'] = main_region.get('end_column')
            self.logger.info(f"[TARGET] 使用者指定主要區間: {code_regions['main_start']}-{code_regions['main_end']}")
        
        # 處理備用區間
        backup_region = request_data.get('backup_region')
        if backup_region:
            code_regions['backup_start'] = backup_region.get('start_column')
            code_regions['backup_end'] = backup_region.get('end_column')
            self.logger.info(f"[TARGET] 使用者指定備用區間: {code_regions['backup_start']}-{code_regions['backup_end']}")
        
        # 如果沒有使用者指定的區間，返回 None 以使用自動檢測
        if not code_regions:
            self.logger.info("[SEARCH] 未指定 CODE 區間，將使用自動檢測")
            return None
        else:
            self.logger.info(f"[OK] 使用指定的 CODE 區間設定: {code_regions}")
            return code_regions
    
    async def _execute_stage1_processing(self, folder_path: str) -> Dict[str, Any]:
        """執行第一階段處理：EQC Bin1 Final Processor"""
        try:
            self.logger.info("[CHART] 第一階段：執行 EQC Bin1 Final Processor")
            
            from src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import EQCBin1FinalProcessorV2
            stage1_processor = EQCBin1FinalProcessorV2()
            
            stage1_result_tuple = stage1_processor.process_complete_eqc_integration(
                folder_path,
                enable_debug_log=True
            )
            
            # 處理第一階段返回的tuple結果
            if stage1_result_tuple[0] is None or stage1_result_tuple[1] is None:
                self.logger.error("[ERROR] 第一階段處理失敗: EQCTOTALDATA.csv生成失敗")
                return {
                    "status": "error",
                    "message": "第一階段處理失敗: EQCTOTALDATA.csv生成失敗"
                }
            
            self.logger.info("[OK] 第一階段處理完成")
            return {
                "status": "success",
                "eqctotaldata_path": stage1_result_tuple[0],
                "eqctotaldata_raw_path": stage1_result_tuple[1]
            }
            
        except Exception as e:
            self.logger.error(f"第一階段處理失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"第一階段處理失敗: {str(e)}"
            }
    
    async def _execute_stage2_processing(self, folder_path: str, code_regions: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """執行第二階段處理：Standard EQC Processor"""
        try:
            self.logger.info("[REFRESH] 第二階段：執行 Standard EQC Processor")
            
            from src.infrastructure.adapters.excel.eqc.processors.eqc_standard_processor import StandardEQCProcessor
            stage2_processor = StandardEQCProcessor()
            
            # 執行第二階段處理，確保啟用所有必要步驟
            stage2_result = stage2_processor.process_from_stage2_only(
                folder_path,
                include_inseqcrtdata2=True,
                include_step5_testflow=True,
                include_step6_excel=True,
                include_final_excel_conversion=True,
                code_regions=code_regions
            )
            
            # 檢查第二階段結果
            if stage2_result['status'] != 'success':
                self.logger.error(f"[ERROR] 第二階段處理失敗: {stage2_result.get('message', '未知錯誤')}")
                return {
                    "status": "error",
                    "message": f"第二階段處理失敗: {stage2_result.get('message', '未知錯誤')}"
                }
            
            self.logger.info("[OK] 第二階段處理完成")
            return stage2_result
            
        except Exception as e:
            self.logger.error(f"第二階段處理失敗: {str(e)}")
            return {
                "status": "error",
                "message": f"第二階段處理失敗: {str(e)}"
            }
    
    async def _execute_post_processing(self, folder_path: str) -> Dict[str, Any]:
        """執行後處理：CSV to Summary"""
        try:
            self.logger.info("[CHART] 執行 CSV to Summary 處理...")
            
            from csv_to_summary import main as csv_to_summary_main
            
            # 建立模擬的命令列參數
            import sys
            original_argv = sys.argv
            
            try:
                # 設定 csv_to_summary 的參數
                sys.argv = ['csv_to_summary.py', folder_path]
                
                # 呼叫 csv_to_summary 的 main 函式
                result = csv_to_summary_main()
                summary_generated = (result == 0)
                
                if summary_generated:
                    self.logger.info("[OK] CSV to Summary 處理完成")
                else:
                    self.logger.warning("[WARNING] CSV to Summary 處理失敗")
                
                return {"status": "success", "summary_generated": summary_generated}
                
            except Exception as e:
                self.logger.error(f"[ERROR] CSV to Summary 執行失敗: {e}")
                return {"status": "warning", "summary_generated": False, "error": str(e)}
            finally:
                # 恢復原始參數
                sys.argv = original_argv
                
        except ImportError:
            self.logger.warning("[WARNING] 無法匯入 csv_to_summary 模組")
            return {"status": "warning", "summary_generated": False, "error": "模組不存在"}
    
    def _build_final_result(self, stage1_result: Dict[str, Any], stage2_result: Dict[str, Any], 
                           summary_result: Dict[str, Any], processing_time: float, 
                           code_regions: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """構建最終處理結果"""
        
        # 檢查生成的檔案
        folder_path = os.path.dirname(stage1_result.get('eqctotaldata_path', ''))
        eqctotaldata_csv = os.path.join(folder_path, "EQCTOTALDATA.csv")
        eqctotaldata_xlsx = os.path.join(folder_path, "EQCTOTALDATA.xlsx")
        
        processed_result = {
            'status': 'success',
            'eqctotaldata_path': eqctotaldata_xlsx if os.path.exists(eqctotaldata_xlsx) else eqctotaldata_csv,
            'processing_time': processing_time,
            'message': 'EQC 完整流程處理完成',
            'stage2_result': stage2_result
        }
        
        self.logger.info(f"[OK] 兩階段處理完成，耗時 {processing_time:.2f}秒")
        
        # 符合前端期待的格式：integrated_result.results[1]
        return {
            "status": "success", 
            "message": "Step 2 - 程式碼區間檢測與雙重搜尋完成",
            "processing_time": round(processing_time, 2),
            "summary_generated": summary_result.get('summary_generated', False),
            "code_regions": code_regions,
            "integrated_result": {
                "results": [
                    {"status": "skipped", "message": "Step 1 已完成"},
                    {"status": "success", "data": processed_result}
                ]
            }
        }
